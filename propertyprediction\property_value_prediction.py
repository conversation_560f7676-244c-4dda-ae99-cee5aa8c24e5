# -- coding: utf-8 --
"""
Property Value Predictor - End-to-End Machine Learning Project

This project demonstrates:
1. Generating synthetic property data (80 samples) with NumPy (features: area, room_count)
2. Training a Ridge Regression model
3. Using LIME to explain a high-value prediction for a premium property
"""

import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import Ridge
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.metrics import mean_squared_error, r2_score, f1_score, classification_report
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.pipeline import Pipeline
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

# Currency conversion rate (USD to INR)
CURRENCY_MULTIPLIER = 83.0  # Approximate exchange rate

print("Welcome to Real Estate Value Predictor!")
print("=" * 50)
print(f"Note: All values will be displayed in Indian Rupees (INR)")
print(f"Exchange rate used: 1 USD = {CURRENCY_MULTIPLIER} INR")
print("=" * 50)

# Step 1: Generate Synthetic Property Data
def create_synthetic_property_dataset(total_records=80):
    """
    Generate synthetic property data with features: area and room count
    """
    print("Step 1: Creating Synthetic Property Dataset")
    print("-" * 40)
    
    # Generate property areas (in square feet) - normally distributed around 2000 sq ft
    property_areas = np.random.normal(loc=2000, scale=500, size=total_records)
    property_areas = np.clip(property_areas, 800, 4000)  # Clip to reasonable range
    
    # Generate room count - correlated with property area but with some noise
    # Larger properties tend to have more rooms
    base_room_count = property_areas / 400  # Base relationship: ~1 room per 400 sq ft
    random_variation = np.random.normal(0, 1, total_records)  # Add some randomness
    room_quantities = base_room_count + random_variation
    room_quantities = np.clip(room_quantities, 2, 10)  # Clip to reasonable range (2-10 rooms)
    
    # Generate property values with more complex relationships
    # Enhanced value formula with non-linear relationships
    foundation_value_usd = 50000  # Base value in USD

    # Non-linear area contribution (premium for larger properties)
    area_contribution_usd = property_areas * 60 + (property_areas ** 1.2) * 0.5

    # Room contribution with diminishing returns
    room_contribution_usd = room_quantities * 12000 + (room_quantities ** 1.5) * 2000

    # Location premium (simulated based on area - larger properties in better locations)
    location_premium_usd = np.where(property_areas > 2500, property_areas * 25, 0)

    # Age factor (simulated - newer properties worth more)
    property_age = np.random.uniform(1, 30, total_records)  # 1-30 years old
    age_depreciation_usd = property_age * 1000  # Depreciation per year

    # Market noise with heteroscedasticity (larger properties have more variance)
    noise_scale = 15000 + (property_areas - 1000) * 5
    market_noise_usd = np.random.normal(0, noise_scale, total_records)

    property_values_usd = (foundation_value_usd + area_contribution_usd +
                          room_contribution_usd + location_premium_usd -
                          age_depreciation_usd + market_noise_usd)
    property_values_usd = np.clip(property_values_usd, 40000, 1200000)  # Wider range

    # Convert to INR
    property_values_inr = property_values_usd * CURRENCY_MULTIPLIER

    # Create DataFrame with additional features
    property_dataset = pd.DataFrame({
        'area_sqft': property_areas,
        'room_count': room_quantities,
        'property_age': property_age,
        'value_inr': property_values_inr
    })
    
    print(f"Generated {total_records} synthetic property records")
    print(f"Area range: {property_dataset['area_sqft'].min():.0f} - {property_dataset['area_sqft'].max():.0f} sq ft")
    print(f"Room range: {property_dataset['room_count'].min():.1f} - {property_dataset['room_count'].max():.1f}")
    print(f"Value range: ₹{property_dataset['value_inr'].min():,.0f} - ₹{property_dataset['value_inr'].max():,.0f}")
    print()
    
    return property_dataset

# Generate the synthetic data
real_estate_data = create_synthetic_property_dataset(80)

# Step 2: Exploratory Data Analysis
def perform_data_exploration(dataset):
    """
    Perform exploratory data analysis on the property data
    """
    print("Step 2: Comprehensive Data Exploration")
    print("-" * 40)
    
    # Basic statistics
    print("Dataset Information:")
    print(dataset.info())
    print("\nStatistical Summary:")
    print(dataset.describe())
    
    # Correlation analysis
    print("\nCorrelation Analysis:")
    feature_correlation_matrix = dataset.corr()
    print(feature_correlation_matrix)
    
    # Enhanced Visualizations
    _, plot_axes = plt.subplots(2, 3, figsize=(18, 12))

    # 1. Distribution plots
    plot_axes[0, 0].hist(dataset['area_sqft'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    plot_axes[0, 0].set_title('Distribution of Property Areas')
    plot_axes[0, 0].set_xlabel('Area (sq ft)')
    plot_axes[0, 0].set_ylabel('Frequency')

    plot_axes[0, 1].hist(dataset['room_count'], bins=15, alpha=0.7, color='lightgreen', edgecolor='black')
    plot_axes[0, 1].set_title('Distribution of Room Counts')
    plot_axes[0, 1].set_xlabel('Number of Rooms')
    plot_axes[0, 1].set_ylabel('Frequency')

    plot_axes[0, 2].hist(dataset['property_age'], bins=15, alpha=0.7, color='orange', edgecolor='black')
    plot_axes[0, 2].set_title('Distribution of Property Age')
    plot_axes[0, 2].set_xlabel('Age (years)')
    plot_axes[0, 2].set_ylabel('Frequency')

    # 2. Scatter plots
    plot_axes[1, 0].scatter(dataset['area_sqft'], dataset['value_inr'], alpha=0.6, color='coral')
    plot_axes[1, 0].set_title('Property Area vs Value')
    plot_axes[1, 0].set_xlabel('Area (sq ft)')
    plot_axes[1, 0].set_ylabel('Value (₹)')

    plot_axes[1, 1].scatter(dataset['room_count'], dataset['value_inr'], alpha=0.6, color='gold')
    plot_axes[1, 1].set_title('Room Count vs Value')
    plot_axes[1, 1].set_xlabel('Number of Rooms')
    plot_axes[1, 1].set_ylabel('Value (₹)')

    plot_axes[1, 2].scatter(dataset['property_age'], dataset['value_inr'], alpha=0.6, color='purple')
    plot_axes[1, 2].set_title('Property Age vs Value')
    plot_axes[1, 2].set_xlabel('Age (years)')
    plot_axes[1, 2].set_ylabel('Value (₹)')
    
    plt.tight_layout()
    plt.savefig('exploratory_analysis_plots.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Exploratory analysis plots saved as 'exploratory_analysis_plots.png'")

    # Correlation heatmap
    plt.figure(figsize=(8, 6))
    sns.heatmap(feature_correlation_matrix, annot=True, cmap='coolwarm', center=0, 
                square=True, linewidths=0.5)
    plt.title('Feature Correlation Heatmap')
    plt.savefig('feature_correlation_heatmap.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Feature correlation heatmap saved as 'feature_correlation_heatmap.png'")

# Perform EDA
perform_data_exploration(real_estate_data)

# Step 3: Advanced Model Development with Feature Engineering
def build_and_train_advanced_regression_model(property_data):
    """
    Prepare data and train multiple advanced regression models with feature engineering
    """
    print("Step 3: Advanced Data Preprocessing and Model Development")
    print("-" * 40)

    # Enhanced feature engineering
    feature_matrix = property_data[['area_sqft', 'room_count', 'property_age']].copy()

    # Create additional engineered features
    feature_matrix['area_per_room'] = feature_matrix['area_sqft'] / feature_matrix['room_count']
    feature_matrix['area_squared'] = feature_matrix['area_sqft'] ** 2
    feature_matrix['room_squared'] = feature_matrix['room_count'] ** 2
    feature_matrix['area_room_interaction'] = feature_matrix['area_sqft'] * feature_matrix['room_count']
    feature_matrix['age_area_interaction'] = feature_matrix['property_age'] * feature_matrix['area_sqft']
    feature_matrix['luxury_indicator'] = (feature_matrix['area_sqft'] > 2500).astype(int)

    target_values = property_data['value_inr'].copy()

    # Split the data with stratification based on value quartiles
    value_quartiles = pd.qcut(target_values, q=4, labels=False)
    features_train, features_test, values_train, values_test = train_test_split(
        feature_matrix, target_values, test_size=0.2, random_state=42, stratify=value_quartiles
    )

    print(f"Training set size: {features_train.shape[0]} samples")
    print(f"Test set size: {features_test.shape[0]} samples")
    print(f"Number of features: {features_train.shape[1]}")

    # Advanced preprocessing pipeline
    feature_scaler = StandardScaler()
    features_train_scaled = feature_scaler.fit_transform(features_train)
    features_test_scaled = feature_scaler.transform(features_test)

    # Model 1: Optimized Ridge Regression with hyperparameter tuning
    print("\nTraining Ridge Regression with hyperparameter tuning...")
    ridge_params = {'alpha': [0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 20.0]}
    ridge_grid = GridSearchCV(Ridge(random_state=42), ridge_params, cv=5, scoring='r2')
    ridge_grid.fit(features_train_scaled, values_train)
    best_ridge = ridge_grid.best_estimator_

    # Model 2: Random Forest Regressor
    print("Training Random Forest Regressor...")
    rf_params = {
        'n_estimators': [100, 200],
        'max_depth': [10, 15, 20],
        'min_samples_split': [2, 5],
        'min_samples_leaf': [1, 2]
    }
    rf_grid = GridSearchCV(RandomForestRegressor(random_state=42), rf_params, cv=3, scoring='r2')
    rf_grid.fit(features_train, values_train)  # RF doesn't need scaling
    best_rf = rf_grid.best_estimator_

    # Model 3: Gradient Boosting Regressor
    print("Training Gradient Boosting Regressor...")
    gb_params = {
        'n_estimators': [100, 150],
        'learning_rate': [0.05, 0.1, 0.15],
        'max_depth': [3, 5, 7]
    }
    gb_grid = GridSearchCV(GradientBoostingRegressor(random_state=42), gb_params, cv=3, scoring='r2')
    gb_grid.fit(features_train, values_train)  # GB doesn't need scaling
    best_gb = gb_grid.best_estimator_

    # Make predictions with all models
    ridge_train_pred = best_ridge.predict(features_train_scaled)
    ridge_test_pred = best_ridge.predict(features_test_scaled)

    rf_train_pred = best_rf.predict(features_train)
    rf_test_pred = best_rf.predict(features_test)

    gb_train_pred = best_gb.predict(features_train)
    gb_test_pred = best_gb.predict(features_test)

    # Ensemble prediction (weighted average)
    ensemble_train_pred = (0.3 * ridge_train_pred + 0.35 * rf_train_pred + 0.35 * gb_train_pred)
    ensemble_test_pred = (0.3 * ridge_test_pred + 0.35 * rf_test_pred + 0.35 * gb_test_pred)

    # Comprehensive Model Evaluation
    models = {
        'Ridge': (ridge_train_pred, ridge_test_pred),
        'Random Forest': (rf_train_pred, rf_test_pred),
        'Gradient Boosting': (gb_train_pred, gb_test_pred),
        'Ensemble': (ensemble_train_pred, ensemble_test_pred)
    }

    print(f"\nComprehensive Model Performance Comparison:")
    print("=" * 60)

    best_model_name = ""
    best_test_r2 = -1
    best_predictions = None

    for model_name, (train_pred, test_pred) in models.items():
        train_rmse = np.sqrt(mean_squared_error(values_train, train_pred))
        test_rmse = np.sqrt(mean_squared_error(values_test, test_pred))
        train_r2 = r2_score(values_train, train_pred)
        test_r2 = r2_score(values_test, test_pred)

        print(f"\n{model_name} Model:")
        print(f"  Training RMSE: ₹{train_rmse:,.2f}")
        print(f"  Test RMSE: ₹{test_rmse:,.2f}")
        print(f"  Training R²: {train_r2:.4f}")
        print(f"  Test R²: {test_r2:.4f}")

        if test_r2 > best_test_r2:
            best_test_r2 = test_r2
            best_model_name = model_name
            best_predictions = (train_pred, test_pred)

    print(f"\n🏆 Best Model: {best_model_name} (Test R²: {best_test_r2:.4f})")

    # Use best model for F1 score calculation
    values_train_predicted, values_test_predicted = best_predictions

    # Enhanced F1 Score calculation with more categories
    value_quantiles = np.quantile(values_train, [0.25, 0.5, 0.75])

    def value_to_category(property_values):
        categories = np.zeros(len(property_values), dtype=int)
        categories[property_values <= value_quantiles[0]] = 0  # Low
        categories[(property_values > value_quantiles[0]) & (property_values <= value_quantiles[1])] = 1  # Medium-Low
        categories[(property_values > value_quantiles[1]) & (property_values <= value_quantiles[2])] = 2  # Medium-High
        categories[property_values > value_quantiles[2]] = 3  # High
        return categories

    # Convert actual and predicted values to categories
    values_train_categories = value_to_category(values_train)
    values_test_categories = value_to_category(values_test)
    values_train_pred_categories = value_to_category(values_train_predicted)
    values_test_pred_categories = value_to_category(values_test_predicted)

    # Calculate F1 scores
    training_f1 = f1_score(values_train_categories, values_train_pred_categories, average='weighted')
    testing_f1 = f1_score(values_test_categories, values_test_pred_categories, average='weighted')

    print(f"\nEnhanced F1 Scores (4 Value Categories):")
    print(f"Training F1: {training_f1:.4f}")
    print(f"Test F1: {testing_f1:.4f}")

    # Print classification report for test set
    print(f"\nClassification Report (Test Set):")
    category_labels = ['Low Value', 'Medium-Low Value', 'Medium-High Value', 'High Value']
    print(classification_report(values_test_categories, values_test_pred_categories, target_names=category_labels))

    # Display best model information
    if best_model_name == 'Ridge':
        best_model_obj = best_ridge
        print(f"\nBest Model ({best_model_name}) Coefficients:")
        feature_names = features_train.columns
        for i, coefficient in enumerate(best_model_obj.coef_):
            print(f"  {feature_names[i]}: {coefficient:.2f}")
        print(f"  Intercept: {best_model_obj.intercept_:.2f}")
    elif best_model_name == 'Random Forest':
        best_model_obj = best_rf
        print(f"\nBest Model ({best_model_name}) Feature Importances:")
        feature_names = features_train.columns
        importances = best_model_obj.feature_importances_
        for i, importance in enumerate(importances):
            print(f"  {feature_names[i]}: {importance:.4f}")
    elif best_model_name == 'Gradient Boosting':
        best_model_obj = best_gb
        print(f"\nBest Model ({best_model_name}) Feature Importances:")
        feature_names = features_train.columns
        importances = best_model_obj.feature_importances_
        for i, importance in enumerate(importances):
            print(f"  {feature_names[i]}: {importance:.4f}")
    else:
        best_model_obj = best_ridge  # Default to ridge for ensemble
        print(f"\nEnsemble Model combines all three models")

    # Enhanced visualization with multiple models
    plt.figure(figsize=(16, 12))

    # Plot 1: Model comparison
    plt.subplot(2, 3, 1)
    model_names = list(models.keys())
    test_r2_scores = [r2_score(values_test, models[name][1]) for name in model_names]
    colors = ['blue', 'green', 'orange', 'red']
    bars = plt.bar(model_names, test_r2_scores, color=colors, alpha=0.7)
    plt.title('Model Performance Comparison (Test R²)')
    plt.ylabel('R² Score')
    plt.xticks(rotation=45)
    for i, bar in enumerate(bars):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{test_r2_scores[i]:.3f}', ha='center', va='bottom')

    # Plot 2: Best model predictions vs actual
    plt.subplot(2, 3, 2)
    plt.scatter(values_train, values_train_predicted, alpha=0.6, color='blue', label='Training', s=30)
    plt.scatter(values_test, values_test_predicted, alpha=0.8, color='red', label='Test', s=30)
    plt.plot([target_values.min(), target_values.max()], [target_values.min(), target_values.max()], 'k--', lw=2)
    plt.xlabel('Actual Value (₹)')
    plt.ylabel('Predicted Value (₹)')
    plt.title(f'{best_model_name} Model: Actual vs Predicted')
    plt.legend()

    # Plot 3: Residual analysis
    plt.subplot(2, 3, 3)
    training_residuals = values_train - values_train_predicted
    testing_residuals = values_test - values_test_predicted
    plt.scatter(values_train_predicted, training_residuals, alpha=0.6, color='blue', label='Training', s=30)
    plt.scatter(values_test_predicted, testing_residuals, alpha=0.8, color='red', label='Test', s=30)
    plt.axhline(y=0, color='k', linestyle='--')
    plt.xlabel('Predicted Value (₹)')
    plt.ylabel('Residuals (₹)')
    plt.title('Residual Analysis')
    plt.legend()

    # Plot 4: Feature importance (if available)
    plt.subplot(2, 3, 4)
    if hasattr(best_model_obj, 'feature_importances_'):
        feature_names = features_train.columns
        importances = best_model_obj.feature_importances_
        indices = np.argsort(importances)[::-1]
        plt.bar(range(len(importances)), importances[indices], alpha=0.7)
        plt.title(f'{best_model_name} Feature Importances')
        plt.xticks(range(len(importances)), [feature_names[i] for i in indices], rotation=45)
    else:
        plt.text(0.5, 0.5, f'{best_model_name}\nCoefficients shown\nin console output',
                ha='center', va='center', transform=plt.gca().transAxes)
        plt.title('Model Information')

    # Plot 5: Error distribution
    plt.subplot(2, 3, 5)
    plt.hist(testing_residuals, bins=15, alpha=0.7, color='orange', edgecolor='black')
    plt.xlabel('Residuals (₹)')
    plt.ylabel('Frequency')
    plt.title('Test Set Residuals Distribution')
    plt.axvline(x=0, color='red', linestyle='--')

    # Plot 6: Learning curves (for ensemble)
    plt.subplot(2, 3, 6)
    train_sizes = np.linspace(0.1, 1.0, 10)
    train_scores = []
    val_scores = []

    for train_size in train_sizes:
        n_samples = int(train_size * len(features_train))
        if n_samples < 5:
            continue
        temp_model = Ridge(alpha=best_ridge.alpha, random_state=42)
        temp_model.fit(features_train_scaled[:n_samples], values_train[:n_samples])
        train_pred = temp_model.predict(features_train_scaled[:n_samples])
        val_pred = temp_model.predict(features_test_scaled)
        train_scores.append(r2_score(values_train[:n_samples], train_pred))
        val_scores.append(r2_score(values_test, val_pred))

    valid_train_sizes = train_sizes[len(train_sizes)-len(train_scores):]
    plt.plot(valid_train_sizes, train_scores, 'o-', color='blue', label='Training')
    plt.plot(valid_train_sizes, val_scores, 'o-', color='red', label='Validation')
    plt.xlabel('Training Set Size')
    plt.ylabel('R² Score')
    plt.title('Learning Curves')
    plt.legend()

    plt.tight_layout()
    plt.savefig('advanced_model_performance.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Advanced model performance plots saved as 'advanced_model_performance.png'")

    print()
    return best_model_obj, feature_scaler, features_train, features_test, values_train, values_test

# Train the advanced model
trained_model, data_scaler, train_features, test_features, train_values, test_values = build_and_train_advanced_regression_model(real_estate_data)

# Step 4: Find a Large Property for High-Value Prediction
def identify_premium_property_for_analysis(property_data, trained_model, data_scaler):
    """
    Find a large property with high predicted value for